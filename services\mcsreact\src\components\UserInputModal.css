.user-input-modal {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.user-input-modal .modal-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1001;
}
.user-input-modal .modal-content {
  background: #fff;
  border-radius: 8px;
  padding: 2rem 2.5rem;
  box-shadow: 0 4px 32px rgba(0,0,0,0.18);
  z-index: 1002;
  min-width: 320px;
  max-width: 90vw;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.user-input-modal .modal-question {
  font-size: 1.1rem;
  margin-bottom: 1rem;
}
.user-input-modal .modal-input {
  margin-bottom: 1.5rem;
}
.user-input-modal .modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}
.user-input-modal .modal-submit {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
}
.user-input-modal .modal-cancel {
  background: #eee;
  color: #333;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  cursor: pointer;
}
.user-input-modal input[type="text"],
.user-input-modal input[type="number"] {
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.user-input-modal button {
  transition: background 0.2s;
}
.user-input-modal .modal-submit:hover {
  background: #115293;
}
.user-input-modal .modal-cancel:hover {
  background: #ccc;
}

/* File Upload Styles */
.file-upload-container {
  width: 100%;
}

.file-drop-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fafafa;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.file-drop-zone:hover {
  border-color: #1976d2;
  background-color: #f5f5f5;
}

.file-drop-zone.drag-over {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
  border-style: solid;
}

.file-drop-zone.has-file {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.upload-icon {
  font-size: 3rem;
  opacity: 0.6;
}

.upload-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.upload-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

.upload-subtitle {
  font-size: 0.9rem;
  color: #666;
}

.file-info {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f0f0f0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-name {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.file-size {
  font-size: 0.9rem;
  color: #666;
}

.remove-file {
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  line-height: 1;
}

.remove-file:hover {
  background: #cc0000;
}
