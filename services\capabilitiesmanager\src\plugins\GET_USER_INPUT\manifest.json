{"id": "plugin-ASK_USER_QUESTION", "verb": "ASK_USER_QUESTION", "description": "Requests input from the user", "explanation": "This plugin sends a question to the user and returns their response", "inputDefinitions": [{"name": "question", "required": true, "type": "string", "description": "The question to ask the user"}, {"name": "choices", "required": false, "type": "array", "description": "Optional array of choices for multiple choice questions"}, {"name": "answerType", "required": false, "type": "string", "description": "Type of answer expected (text, number, boolean, or multipleChoice)"}], "outputDefinitions": [{"name": "answer", "required": false, "type": "string", "description": "The user's response"}], "language": "python", "entryPoint": {"main": "main.py", "packageSource": {"type": "local", "path": "./", "requirements": "requirements.txt"}}, "repository": {"type": "local"}, "security": {"permissions": [], "sandboxOptions": {}, "trust": {"signature": "GEr7jE7Y6GDjfKXD2i1yMrIWPOko7GJxg9jPCNrxCae2pD1pvVXdi0YrTWK4SKGZis1G6GZcoTtrob26xt17Iuu9f8O8gX/Cz433TRKo78Akl5ggnQn8fqj1uQmIco6uGcspMxHF0PuNHTrZF5jKG+jVT2clG7HPkUXEYhRc61kD7Z6MaKAjFmg75JUkyaW5S6hNY1wFnmrTLX37mu017QE+65rZWELHzeGV9nbmataVMzCjPZmcvn583tCZTs+H9sC8iVr8kKwvCJ2Y3grmSnr6/8biKgQLlpIQp9x9vv7TuyMV7obicGkgkfvt6HQquynHZA0ForXKwwYbth3smg=="}}, "distribution": {"downloads": 0, "rating": 0}, "version": "1.0.0"}